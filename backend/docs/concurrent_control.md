# 并发任务控制功能

## 概述

为了防止系统过载和确保稳定性，我们实现了并发任务控制功能，限制同时进行的图像生成任务数量不超过5个。

## 功能特性

### 1. 并发限制
- **最大并发任务数**: 5个
- **控制范围**: 所有图像生成任务（包括角色图和场景图）
- **等待机制**: 超过限制的任务会等待前面的任务完成

### 2. 任务类型
支持的任务类型：
- 普通文本生图任务 (`create_generation_task`)
- 多图生图任务 (`create_multi_edit_task`)
- 角色基准图生成
- 场景图片生成

### 3. 监控功能
- 实时并发状态查询
- 活跃任务列表
- 可用槽位数量
- 任务状态统计

## API接口

### 获取并发状态
```http
GET /api/concurrent-status
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "concurrent_info": {
      "max_concurrent_tasks": 5,
      "active_task_count": 2,
      "available_slots": 3,
      "active_task_ids": ["task_1", "task_2"],
      "semaphore_value": 3
    },
    "task_summary": {
      "total_tasks": 10,
      "pending": 0,
      "processing": 2,
      "success": 7,
      "failed": 1,
      "tasks": [...]
    },
    "timestamp": 1703123456.789
  }
}
```

## 实现原理

### 1. 信号量控制
使用 `asyncio.Semaphore` 来控制并发数量：
```python
self.task_semaphore = asyncio.Semaphore(max_concurrent_tasks)
```

### 2. 任务生命周期管理
```python
async with self.task_semaphore:
    # 获取任务槽位
    self.active_tasks[task_id] = task
    try:
        # 执行任务创建
        # ...
    finally:
        # 释放任务槽位
        del self.active_tasks[task_id]
```

### 3. 层级控制
- **RemoteImageAPI**: 底层并发控制
- **AsyncImageManager**: 使用API的并发控制，不再重复限制

## 配置参数

### 环境变量
可以通过环境变量配置：
```bash
# 最大并发任务数（默认5）
MAX_CONCURRENT_TASKS=5
```

### 代码配置
```python
# 创建API实例时指定
api = RemoteImageAPI(max_concurrent_tasks=5)

# 创建管理器时指定
manager = AsyncImageManager(max_concurrent_tasks=5)
```

## 使用示例

### 1. 检查可用槽位
```python
# 等待可用槽位
has_slot = await api.wait_for_available_slot(timeout=60.0)
if has_slot:
    # 创建任务
    task = await api.create_generation_task(...)
```

### 2. 监控并发状态
```python
# 获取并发信息
info = api.get_concurrent_task_info()
print(f"活跃任务数: {info['active_task_count']}")
print(f"可用槽位: {info['available_slots']}")
```

### 3. 批量任务处理
```python
# 批量创建任务（自动排队）
tasks = []
for i in range(10):  # 创建10个任务
    task = await api.create_generation_task(f"task_{i}", f"prompt {i}")
    tasks.append(task)
```

## 错误处理

### 1. 超过并发限制
当API服务器返回并发限制错误时：
```
"Exceed Concurrent Jobs"
```

任务状态会被设置为 `FAILED`，错误信息会记录在 `error_message` 中。

### 2. 超时处理
等待槽位超时：
```python
try:
    has_slot = await api.wait_for_available_slot(timeout=30.0)
    if not has_slot:
        print("等待槽位超时")
except asyncio.TimeoutError:
    print("等待槽位超时")
```

## 性能影响

### 1. 优势
- **系统稳定性**: 防止过载
- **资源管理**: 合理分配计算资源
- **用户体验**: 避免长时间等待

### 2. 注意事项
- **等待时间**: 高峰期可能需要等待
- **任务规划**: 建议合理安排任务优先级
- **监控重要性**: 定期检查并发状态

## 测试验证

运行测试脚本验证功能：
```bash
cd backend
python test_concurrent_limit.py
```

测试内容：
- 并发限制是否生效
- 任务排队机制
- 槽位管理功能
- 状态监控准确性

## 故障排除

### 1. 任务一直等待
- 检查是否有任务卡住
- 查看活跃任务列表
- 重启服务释放资源

### 2. 并发控制失效
- 检查信号量初始化
- 验证任务创建流程
- 查看错误日志

### 3. 性能问题
- 调整并发数量
- 优化任务处理逻辑
- 监控系统资源使用
