#!/usr/bin/env python3
"""
测试并发状态API端点
"""

import asyncio
import sys
import os
import json
import httpx

# 添加项目根目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(backend_dir, 'src')
sys.path.insert(0, src_dir)


async def test_concurrent_status_api():
    """测试并发状态API"""
    print("🧪 测试并发状态API端点...")
    
    # API基础URL（假设服务运行在8000端口）
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient() as client:
            # 测试并发状态端点
            response = await client.get(f"{base_url}/api/concurrent-status")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ API响应成功")
                print(f"📊 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 验证响应结构
                if "success" in data and "data" in data:
                    concurrent_info = data["data"].get("concurrent_info", {})
                    task_summary = data["data"].get("task_summary", {})
                    
                    print(f"\n📈 并发信息:")
                    print(f"   - 最大并发任务数: {concurrent_info.get('max_concurrent_tasks')}")
                    print(f"   - 当前活跃任务数: {concurrent_info.get('active_task_count')}")
                    print(f"   - 可用槽位: {concurrent_info.get('available_slots')}")
                    print(f"   - 活跃任务ID: {concurrent_info.get('active_task_ids')}")
                    
                    print(f"\n📊 任务统计:")
                    print(f"   - 总任务数: {task_summary.get('total_tasks')}")
                    print(f"   - 处理中: {task_summary.get('processing')}")
                    print(f"   - 成功: {task_summary.get('success')}")
                    print(f"   - 失败: {task_summary.get('failed')}")
                    
                    return True
                else:
                    print("❌ 响应结构不正确")
                    return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
    except httpx.ConnectError:
        print("⚠️  无法连接到API服务器（可能服务未启动）")
        print("请先启动API服务器: uvicorn agent.app:app --host 0.0.0.0 --port 8000")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_api_with_mock_server():
    """使用模拟数据测试API逻辑"""
    print("\n🧪 测试API逻辑（模拟数据）...")
    
    try:
        # 导入必要的模块
        from agent.async_image_manager import AsyncImageManager
        
        # 创建管理器实例
        manager = AsyncImageManager(max_concurrent_tasks=3)
        
        # 获取并发信息
        concurrent_info = manager.get_concurrent_task_info()
        task_summary = manager.get_task_status_summary()
        
        print("✅ 成功获取并发信息")
        print(f"📊 并发信息: {json.dumps(concurrent_info, indent=2, ensure_ascii=False)}")
        print(f"📈 任务统计: {json.dumps(task_summary, indent=2, ensure_ascii=False)}")
        
        # 验证数据结构
        required_concurrent_fields = ['max_concurrent_tasks', 'active_task_count', 'available_slots']
        required_summary_fields = ['total_tasks', 'pending', 'processing', 'success', 'failed']
        
        for field in required_concurrent_fields:
            if field not in concurrent_info:
                print(f"❌ 缺少并发信息字段: {field}")
                return False
        
        for field in required_summary_fields:
            if field not in task_summary:
                print(f"❌ 缺少任务统计字段: {field}")
                return False
        
        print("✅ 数据结构验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_test_request():
    """创建测试用的HTTP请求示例"""
    print("\n📝 创建测试请求示例...")
    
    # 使用curl命令的示例
    curl_command = """
# 测试并发状态API
curl -X GET "http://localhost:8000/api/concurrent-status" \\
     -H "accept: application/json"

# 测试插画生成API（会创建任务）
curl -X POST "http://localhost:8000/api/start-generation" \\
     -H "accept: application/json" \\
     -H "Content-Type: application/json" \\
     -d '{
       "user_input": "小猫在花园里玩耍",
       "style_preference": "anime",
       "num_panels": 2,
       "multi_edit_model_id": "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
     }'
"""
    
    print(curl_command)
    
    # Python requests示例
    python_example = '''
import requests

# 获取并发状态
response = requests.get("http://localhost:8000/api/concurrent-status")
print(response.json())

# 创建插画生成任务
payload = {
    "user_input": "小猫在花园里玩耍",
    "style_preference": "anime", 
    "num_panels": 2,
    "multi_edit_model_id": "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
}
response = requests.post("http://localhost:8000/api/start-generation", json=payload)
print(response.json())
'''
    
    print("\n🐍 Python示例:")
    print(python_example)


async def main():
    """主测试函数"""
    print("🚀 开始并发状态API测试...\n")
    
    try:
        # 测试API逻辑（不需要服务器）
        success1 = await test_api_with_mock_server()
        
        # 测试实际API端点（需要服务器运行）
        success2 = await test_concurrent_status_api()
        
        # 创建测试请求示例
        create_test_request()
        
        if success1:
            print("\n🎉 API逻辑测试通过!")
            if success2:
                print("🎉 API端点测试也通过!")
            else:
                print("⚠️  API端点测试失败（可能服务器未启动）")
            
            print("\n📋 测试总结:")
            print("   ✅ 并发状态API逻辑正确")
            print("   ✅ 数据结构完整")
            print("   ✅ 错误处理完善")
            print("   ✅ 可以监控实时并发状态")
        else:
            print("\n❌ API逻辑测试失败")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
