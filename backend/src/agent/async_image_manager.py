"""异步图像生成管理器"""

import asyncio
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import logging

from agent.remote_image_api import RemoteImageAPI, ModelType, TaskStatus, ImageGenerationTask

logger = logging.getLogger(__name__)


@dataclass
class ImageGenerationRequest:
    """图像生成请求"""
    prompt: str
    style: str
    generation_type: str  # "text2img" or "img2img"
    panel_id: Optional[str] = None
    character_name: Optional[str] = None
    reference_image: Optional[str] = None
    width: int = 768
    height: int = 1344


class AsyncImageManager:
    """异步图像生成管理器"""

    def __init__(self, max_concurrent_tasks: int = 5):
        self.api = RemoteImageAPI(max_concurrent_tasks=max_concurrent_tasks)
        self.max_concurrent_tasks = max_concurrent_tasks
        # 移除本地信号量，使用RemoteImageAPI的并发控制
        self.style_to_model = {
            "anime": ModelType.ANIME,
            "realistic": ModelType.REALISTIC,
            "art": ModelType.ART,
            "cartoon": ModelType.ANIME,  # 卡通使用动漫模型
            "cyberpunk": ModelType.REALISTIC,  # 赛博朋克使用真实模型
            "watercolor": ModelType.ART,  # 水彩使用艺术模型
            "sketch": ModelType.ART  # 素描使用艺术模型
        }
    
    def _get_model_type(self, style: str) -> ModelType:
        """根据风格获取模型类型"""
        return self.style_to_model.get(style.lower(), ModelType.ANIME)
    
    def _generate_task_id(self, prefix: str = "img") -> str:
        """生成任务ID"""
        return f"{prefix}_{uuid.uuid4().hex[:8]}"
    
    async def _create_single_character_task(
        self,
        character: Dict[str, Any],
        style: str,
        model_type: Any
    ) -> tuple[Any, str]:
        """
        创建单个角色图片生成任务（并发控制由RemoteImageAPI处理）

        Args:
            character: 角色信息
            style: 艺术风格
            model_type: 模型类型

        Returns:
            (任务对象, 角色名称)
        """
        task_id = self._generate_task_id("char")

        # 构建角色提示词（使用英文描述）
        prompt = f"{character['appearance']}, {style} style, character reference sheet, full body, white background, high quality, detailed"

        # 创建任务（并发控制由RemoteImageAPI处理）
        task = await self.api.create_generation_task(
            task_id=task_id,
            prompt=prompt,
            model_type=model_type,
            width=512,
            height=768  # 角色图使用竖向比例
        )

        logger.info(f"Created character generation task for {character['name']}: {task_id}")
        return task, character["name"]

    async def generate_character_images(
        self,
        characters: List[Dict[str, Any]],
        style: str,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, str]:
        """
        生成角色基准图（带并发控制）

        Args:
            characters: 角色信息列表
            style: 艺术风格
            progress_callback: 进度回调函数

        Returns:
            角色名称到图片URL的映射
        """
        if not characters:
            return {}

        model_type = self._get_model_type(style)

        # 筛选需要生成的角色（主角或限制数量）
        characters_to_generate = []
        for character in characters:
            if character.get("role") == "main" or len(characters_to_generate) < 3:
                characters_to_generate.append(character)

        if not characters_to_generate:
            return {}

        # 并发创建任务（受信号量控制）
        task_creation_coroutines = [
            self._create_single_character_task(character, style, model_type)
            for character in characters_to_generate
        ]

        # 等待所有任务创建完成
        task_results = await asyncio.gather(*task_creation_coroutines)

        tasks = []
        task_to_character = {}
        for task, character_name in task_results:
            tasks.append(task)
            task_to_character[task.task_id] = character_name

        # 等待任务完成
        task_ids = [task.task_id for task in tasks]

        def progress_wrapper(completed: int, total: int):
            if progress_callback:
                progress_callback(f"角色基准图生成: {completed}/{total}")

        completed_tasks = await self.api.wait_for_tasks_completion(
            task_ids,
            progress_callback=progress_wrapper
        )

        # 整理结果
        character_images = {}
        for task_id, task in completed_tasks.items():
            character_name = task_to_character.get(task_id)
            if character_name and task.status == TaskStatus.SUCCESS and task.result_urls:
                character_images[character_name] = task.result_urls[0]
                logger.info(f"Character {character_name} image generated: {task.result_urls[0]}")
            elif character_name:
                logger.error(f"Character {character_name} generation failed: {task.error_message}")

        return character_images
    
    async def _create_single_scene_task(
        self,
        prompt_data: Dict[str, Any],
        style: str,
        model_type: Any
    ) -> tuple[Any, Dict[str, Any]]:
        """
        创建单个场景图片生成任务（并发控制由RemoteImageAPI处理）

        Args:
            prompt_data: 场景提示词数据
            style: 艺术风格
            model_type: 模型类型

        Returns:
            (任务对象, 面板信息)
        """
        task_id = self._generate_task_id("scene")
        panel_id = prompt_data.get("panel_id", "0")
        prompt = prompt_data.get("prompt", "")
        generation_type = prompt_data.get("generation_type", "text2img")

        # 创建任务（并发控制由RemoteImageAPI处理）
        task = await self.api.create_generation_task(
            task_id=task_id,
            prompt=prompt,
            model_type=model_type,
            width=768,
            height=512  # 场景图使用横向比例
        )

        panel_info = {
            "panel_id": panel_id,
            "prompt": prompt,
            "generation_type": generation_type
        }

        logger.info(f"Created scene generation task for panel {panel_id}: {task_id}")
        return task, panel_info

    async def generate_scene_images(
        self,
        scene_prompts: List[Dict[str, Any]],
        character_images: Dict[str, str],
        style: str,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """
        生成场景图片（带并发控制）

        Args:
            scene_prompts: 场景提示词列表
            character_images: 角色基准图映射
            style: 艺术风格
            progress_callback: 进度回调函数

        Returns:
            生成结果列表
        """
        if not scene_prompts:
            return []

        model_type = self._get_model_type(style)

        # 并发创建任务（受信号量控制）
        task_creation_coroutines = [
            self._create_single_scene_task(prompt_data, style, model_type)
            for prompt_data in scene_prompts
        ]

        # 等待所有任务创建完成
        task_results = await asyncio.gather(*task_creation_coroutines)

        tasks = []
        task_to_panel = {}
        for task, panel_info in task_results:
            tasks.append(task)
            task_to_panel[task.task_id] = panel_info

        if not tasks:
            return []

        # 等待任务完成
        task_ids = [task.task_id for task in tasks]

        def progress_wrapper(completed: int, total: int):
            if progress_callback:
                progress_callback(f"场景图片生成: {completed}/{total}")

        completed_tasks = await self.api.wait_for_tasks_completion(
            task_ids,
            progress_callback=progress_wrapper
        )

        # 整理结果
        generated_images = []
        for task_id, task in completed_tasks.items():
            panel_info = task_to_panel.get(task_id, {})
            panel_id = panel_info.get("panel_id", "0")

            result = {
                "panel_id": panel_id,
                "prompt_used": panel_info.get("prompt", ""),
                "generation_type": panel_info.get("generation_type", "text2img"),
                "success": task.status == TaskStatus.SUCCESS,
                "image_url": "",
                "error": ""
            }

            if task.status == TaskStatus.SUCCESS and task.result_urls:
                result["image_url"] = task.result_urls[0]
                logger.info(f"Panel {panel_id} image generated: {task.result_urls[0]}")
            else:
                result["error"] = task.error_message
                logger.error(f"Panel {panel_id} generation failed: {task.error_message}")

            generated_images.append(result)

        # 按panel_id排序（转换为数字进行排序）
        generated_images.sort(key=lambda x: int(x["panel_id"]) if x["panel_id"].isdigit() else 0)

        return generated_images
    
    async def generate_all_images(
        self,
        characters: List[Dict[str, Any]],
        scene_prompts: List[Dict[str, Any]],
        style: str,
        progress_callback: Optional[Callable] = None
    ) -> tuple[Dict[str, str], List[Dict[str, Any]]]:
        """
        生成所有图片（角色基准图 + 场景图片）
        
        Args:
            characters: 角色信息列表
            scene_prompts: 场景提示词列表
            style: 艺术风格
            progress_callback: 进度回调函数
            
        Returns:
            (角色图片映射, 场景图片列表)
        """
        # 先生成角色基准图
        if progress_callback:
            progress_callback("开始生成角色基准图...")
        
        character_images = await self.generate_character_images(
            characters, 
            style, 
            progress_callback
        )
        
        # 再生成场景图片
        if progress_callback:
            progress_callback("开始生成场景图片...")
        
        scene_images = await self.generate_scene_images(
            scene_prompts,
            character_images,
            style,
            progress_callback
        )
        
        if progress_callback:
            progress_callback("图片生成完成！")

        return character_images, scene_images

    async def _create_single_multi_scene_task(
        self,
        prompt_data: Dict[str, Any],
        character_images: Dict[str, str],
        style: str,
        model_id: str = "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
    ) -> tuple[Any, Dict[str, Any]]:
        """
        创建单个多图生图任务（带并发控制）

        Args:
            prompt_data: 场景提示词数据
            character_images: 角色图片映射
            style: 艺术风格

        Returns:
            (任务对象, 面板信息)
        """
        task_id = self._generate_task_id("multi_scene")
        panel_id = prompt_data.get("panel_id", "0")
        prompt = prompt_data.get("prompt", "")
        characters_involved = prompt_data.get("characters_involved", [])

        # 收集涉及角色的图片URL
        involved_image_urls = []
        for char_name in characters_involved:
            if char_name in character_images:
                involved_image_urls.append(character_images[char_name])

        panel_info = {
            "panel_id": panel_id,
            "prompt": prompt,
            "characters_involved": characters_involved
        }

        # 如果有角色图片，使用多图生图；否则使用普通生图（并发控制由RemoteImageAPI处理）
        if involved_image_urls:
            # 创建多图生图任务
            task = await self.api.create_multi_edit_task(
                task_id=task_id,
                prompt=prompt,
                image_urls=involved_image_urls,
                width=1024,
                height=1024,
                model_id=model_id
            )
            panel_info["generation_type"] = "multi_edit"
            logger.info(f"Created multi-edit task for panel {panel_id} with {len(involved_image_urls)} character images: {task_id}")
        else:
            # 回退到普通生图
            model_type = self._get_model_type(style)
            task = await self.api.create_generation_task(
                task_id=task_id,
                prompt=prompt,
                model_type=model_type,
                width=1024,
                height=1024
            )
            panel_info["generation_type"] = "text2img"
            logger.info(f"Created text2img task for panel {panel_id}: {task_id}")

        return task, panel_info

    async def generate_scene_with_characters(
        self,
        scene_prompts: List[Dict[str, Any]],
        character_images: Dict[str, str],
        style: str,
        progress_callback: Optional[Callable] = None,
        model_id: str = "flux3dc-cff2-4177-ad3a-28d9b4d3ff48"
    ) -> List[Dict[str, Any]]:
        """
        使用角色基准图生成场景图片（多图生图，带并发控制）

        Args:
            scene_prompts: 场景提示词列表
            character_images: 角色图片映射 {角色名: 图片URL}
            style: 艺术风格
            progress_callback: 进度回调函数

        Returns:
            场景图片列表
        """
        if progress_callback:
            progress_callback("开始使用角色基准图生成场景...")

        if not scene_prompts:
            return []

        # 并发创建任务（受信号量控制）
        task_creation_coroutines = [
            self._create_single_multi_scene_task(prompt_data, character_images, style, model_id)
            for prompt_data in scene_prompts
        ]

        # 等待所有任务创建完成
        task_results = await asyncio.gather(*task_creation_coroutines)

        tasks = []
        task_to_panel = {}
        for task, panel_info in task_results:
            tasks.append(task)
            task_to_panel[task.task_id] = panel_info

        if not tasks:
            return []

        # 等待任务完成
        task_ids = [task.task_id for task in tasks]

        def progress_update(completed, total):
            if progress_callback:
                progress_callback(f"场景生成进度: {completed}/{total}")

        completed_tasks = await self.api.wait_for_tasks_completion(task_ids, progress_update)

        # 整理结果
        scene_images = []
        for task_id, task in completed_tasks.items():
            panel_info = task_to_panel[task_id]

            if task.status == TaskStatus.SUCCESS and task.result_urls:
                scene_images.append({
                    "panel_id": panel_info["panel_id"],
                    "image_url": task.result_urls[0],
                    "prompt": panel_info["prompt"],
                    "generation_type": panel_info["generation_type"],
                    "characters_involved": panel_info["characters_involved"],
                    "success": True
                })
            else:
                scene_images.append({
                    "panel_id": panel_info["panel_id"],
                    "image_url": None,
                    "prompt": panel_info["prompt"],
                    "generation_type": panel_info["generation_type"],
                    "characters_involved": panel_info["characters_involved"],
                    "success": False,
                    "error": task.error_message
                })

        # 按panel_id排序（转换为数字进行排序）
        scene_images.sort(key=lambda x: int(x["panel_id"]) if x["panel_id"].isdigit() else 0)

        if progress_callback:
            success_count = sum(1 for img in scene_images if img["success"])
            progress_callback(f"场景生成完成: {success_count}/{len(scene_images)} 成功")

        return scene_images
    
    def get_task_status_summary(self) -> Dict[str, Any]:
        """获取任务状态摘要"""
        all_tasks = self.api.get_all_tasks_status()

        summary = {
            "total_tasks": len(all_tasks),
            "pending": 0,
            "processing": 0,
            "success": 0,
            "failed": 0,
            "tasks": []
        }

        for task_id, task in all_tasks.items():
            summary[task.status.value] += 1
            summary["tasks"].append({
                "task_id": task_id,
                "status": task.status.value,
                "prompt": task.prompt[:50] + "..." if len(task.prompt) > 50 else task.prompt,
                "created_at": task.created_at,
                "completed_at": task.completed_at,
                "result_count": len(task.result_urls),
                "error": task.error_message
            })

        return summary

    def get_concurrent_task_info(self) -> Dict[str, Any]:
        """获取并发任务信息"""
        return self.api.get_concurrent_task_info()

    async def wait_for_available_slot(self, timeout: float = 60.0) -> bool:
        """等待可用的任务槽位"""
        return await self.api.wait_for_available_slot(timeout)


# 全局实例
async_image_manager = AsyncImageManager()
