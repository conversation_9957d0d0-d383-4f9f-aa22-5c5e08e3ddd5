import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Palette, BookOpen, Sparkles, Image } from "lucide-react";

interface IllustrationWelcomeProps {
  onSubmit: (userInput: string, stylePreference: string, numPanels: number) => void;
  isLoading: boolean;
}

const STYLE_OPTIONS = [
  {
    id: "anime",
    name: "动漫风格",
    description: "日式动漫插画风格，适合二次元角色和场景",
    icon: "🎌",
    recommended: true
  },
  {
    id: "realistic",
    name: "写实风格",
    description: "真实感强的插画风格，适合现实主义场景",
    icon: "📸",
    recommended: false
  },
  {
    id: "art",
    name: "艺术风格",
    description: "艺术化的插画风格，具有独特的美学表现",
    icon: "🎨",
    recommended: false
  },
];

const PANEL_OPTIONS = [
  { value: 0, label: "智能判断", description: "AI根据故事内容自动判断最佳分镜数量", recommended: true },
  { value: 1, label: "1格", description: "单幅插画" },
  { value: 2, label: "2格漫画", description: "简洁的双格展示" },
  { value: 3, label: "3格漫画", description: "三格故事展示" },
  { value: 4, label: "4格漫画", description: "经典的四格漫画" },
  { value: 5, label: "5格漫画", description: "五格故事展示" },
  { value: 6, label: "6格漫画", description: "详细的六格展示" },
  { value: 8, label: "8格漫画", description: "丰富的八格故事" },
  { value: 10, label: "10格漫画", description: "详细的十格故事" },
  { value: 12, label: "12格漫画", description: "完整的十二格故事" },
  { value: 15, label: "15格漫画", description: "丰富的十五格故事" },
  { value: 20, label: "20格漫画", description: "超详细的二十格故事" },
];

const EXAMPLE_STORIES = [
  "小红帽去森林里看望奶奶，路上遇到了大灰狼",
  "勇敢的骑士踏上拯救被困公主的冒险之旅",
  "小猫咪在花园里追逐蝴蝶，发现了一个神秘的洞穴",
  "机器人助手帮助人类在火星上建立新的家园",
  "魔法师学徒在图书馆里学习古老的咒语",
];

export function IllustrationWelcome({ onSubmit, isLoading }: IllustrationWelcomeProps) {
  const [userInput, setUserInput] = useState("");
  const [stylePreference, setStylePreference] = useState("anime");
  const [numPanels, setNumPanels] = useState(0); // 默认使用智能判断

  const handleSubmit = () => {
    if (userInput.trim()) {
      onSubmit(userInput, stylePreference, numPanels);
    }
  };

  const handleExampleClick = (example: string) => {
    setUserInput(example);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6">
      <div className="w-full max-w-4xl space-y-8">
        {/* 标题区域 */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              AI插画生成器
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            输入您的故事，AI将自动生成精美的插画作品
          </p>
          <div className="flex items-center justify-center gap-6 text-sm text-gray-400">
            <div className="flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              <span>智能故事处理</span>
            </div>
            <div className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              <span>多种艺术风格</span>
            </div>
            <div className="flex items-center gap-2">
              <Image className="w-4 h-4" />
              <span>角色一致性</span>
            </div>
          </div>
        </div>

        {/* 主要输入区域 */}
        <Card className="bg-white/10 backdrop-blur-sm border-white/20">
          <CardHeader>
            <CardTitle className="text-white">创作您的故事</CardTitle>
            <CardDescription className="text-gray-300">
              描述您想要的故事情节，AI将为您生成相应的插画
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 故事输入 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-200">故事内容</label>
              <Textarea
                placeholder="请输入您的故事内容，可以是简单的描述或完整的故事..."
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                className="min-h-[120px] bg-white/5 border-white/20 text-white placeholder:text-gray-400 resize-none"
                disabled={isLoading}
              />
              
              {/* 示例故事 */}
              <div className="space-y-2">
                <p className="text-xs text-gray-400">点击示例快速开始：</p>
                <div className="flex flex-wrap gap-2">
                  {EXAMPLE_STORIES.map((example, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="cursor-pointer hover:bg-white/10 border-white/30 text-gray-300 hover:text-white transition-colors"
                      onClick={() => handleExampleClick(example)}
                    >
                      {example}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* 配置选项 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 艺术风格选择 */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-200">艺术风格</label>
                <Select value={stylePreference} onValueChange={setStylePreference} disabled={isLoading}>
                  <SelectTrigger className="bg-white/5 border-white/20 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-700">
                    {STYLE_OPTIONS.map((style) => (
                      <SelectItem key={style.id} value={style.id} className="text-white hover:bg-gray-800">
                        <div className="flex items-center gap-3">
                          <span className="text-lg">{style.icon}</span>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{style.name}</span>
                              {style.recommended && (
                                <Badge variant="secondary" className="text-xs bg-green-600 text-white">
                                  推荐
                                </Badge>
                              )}
                            </div>
                            <div className="text-xs text-gray-400">{style.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 分镜数量选择 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-200">分镜数量</label>
                  <Badge variant="outline" className="text-xs border-blue-400 text-blue-400">
                    支持1-20格
                  </Badge>
                </div>
                <p className="text-xs text-gray-400">
                  选择"智能判断"让AI根据故事内容自动确定最佳分镜数量
                </p>
                <Select value={numPanels.toString()} onValueChange={(value) => setNumPanels(parseInt(value))} disabled={isLoading}>
                  <SelectTrigger className="bg-white/5 border-white/20 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-700">
                    {PANEL_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()} className="text-white hover:bg-gray-800">
                        <div className="flex items-center gap-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{option.label}</span>
                              {option.recommended && (
                                <Badge variant="secondary" className="text-xs bg-blue-600 text-white">
                                  推荐
                                </Badge>
                              )}
                            </div>
                            <div className="text-xs text-gray-400">{option.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 生成按钮 */}
            <Button
              onClick={handleSubmit}
              disabled={!userInput.trim() || isLoading}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium py-3 text-lg"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  生成中...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  开始生成插画
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* 功能特色 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div className="p-4 bg-white/5 rounded-lg border border-white/10">
            <BookOpen className="w-8 h-8 mx-auto mb-2 text-blue-400" />
            <h3 className="font-medium text-white mb-1">智能故事处理</h3>
            <p className="text-xs text-gray-400">自动扩写简短故事或总结复杂情节，智能判断最佳分镜数量</p>
          </div>
          <div className="p-4 bg-white/5 rounded-lg border border-white/10">
            <Palette className="w-8 h-8 mx-auto mb-2 text-purple-400" />
            <h3 className="font-medium text-white mb-1">专业艺术风格</h3>
            <p className="text-xs text-gray-400">支持动漫、写实、艺术三种专业模型</p>
          </div>
          <div className="p-4 bg-white/5 rounded-lg border border-white/10">
            <Image className="w-8 h-8 mx-auto mb-2 text-pink-400" />
            <h3 className="font-medium text-white mb-1">角色一致性</h3>
            <p className="text-xs text-gray-400">保持角色在不同分镜中的外观一致</p>
          </div>
        </div>
      </div>
    </div>
  );
}
